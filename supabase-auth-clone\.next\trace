[{"name":"hot-reloader","duration":267,"timestamp":2033652151,"id":3,"tags":{"version":"15.5.2"},"startTime":1756896761553,"traceId":"5466b6a4b3692588"},{"name":"setup-dev-bundler","duration":1359290,"timestamp":2033647212,"id":2,"parentId":1,"tags":{},"startTime":1756896761548,"traceId":"5466b6a4b3692588"},{"name":"start-dev-server","duration":4360895,"timestamp":2031238228,"id":1,"tags":{"cpus":"12","platform":"win32","memory.freeMem":"777478144","memory.totalMem":"7879585792","memory.heapSizeLimit":"3989831680","memory.rss":"182054912","memory.heapTotal":"99246080","memory.heapUsed":"74268624"},"startTime":1756896759140,"traceId":"5466b6a4b3692588"},{"name":"compile-path","duration":7369687,"timestamp":2037412967,"id":6,"tags":{"trigger":"/"},"startTime":1756896765314,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":7372345,"timestamp":2037412132,"id":5,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756896765313,"traceId":"5466b6a4b3692588"}]
[{"name":"ensure-page","duration":57070,"timestamp":2044795007,"id":7,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756896772696,"traceId":"5466b6a4b3692588"},{"name":"handle-request","duration":9003481,"timestamp":2037396861,"id":4,"tags":{"url":"/"},"startTime":1756896765298,"traceId":"5466b6a4b3692588"},{"name":"memory-usage","duration":11,"timestamp":2046400537,"id":8,"parentId":4,"tags":{"url":"/","memory.rss":"832761856","memory.heapUsed":"162817488","memory.heapTotal":"189022208"},"startTime":1756896774302,"traceId":"5466b6a4b3692588"},{"name":"compile-path","duration":488300,"timestamp":2047242388,"id":11,"tags":{"trigger":"/favicon.ico"},"startTime":1756896775143,"traceId":"5466b6a4b3692588"}]
[{"name":"ensure-page","duration":89460,"timestamp":2047740111,"id":12,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1756896775641,"traceId":"5466b6a4b3692588"},{"name":"handle-request","duration":958899,"timestamp":2047223680,"id":9,"tags":{"url":"/favicon.ico"},"startTime":1756896775125,"traceId":"5466b6a4b3692588"},{"name":"memory-usage","duration":12,"timestamp":2048182688,"id":13,"parentId":9,"tags":{"url":"/favicon.ico","memory.rss":"874971136","memory.heapUsed":"176715480","memory.heapTotal":"201187328"},"startTime":1756896776084,"traceId":"5466b6a4b3692588"},{"name":"client-hmr-latency","duration":2374000,"timestamp":2063527745,"id":16,"parentId":3,"tags":{"updatedModules":["[project]/src/lib/supabase.ts"],"page":"/","isPageHidden":false},"startTime":1756896793826,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":1969428,"timestamp":2064073643,"id":15,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756896791975,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":132783,"timestamp":2065980039,"id":18,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756896793881,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":130401,"timestamp":2066047737,"id":19,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756896793949,"traceId":"5466b6a4b3692588"},{"name":"handle-request","duration":2496719,"timestamp":2064051959,"id":14,"tags":{"url":"/"},"startTime":1756896791953,"traceId":"5466b6a4b3692588"},{"name":"memory-usage","duration":7,"timestamp":2066548762,"id":21,"parentId":14,"tags":{"url":"/","memory.rss":"1050595328","memory.heapUsed":"142443864","memory.heapTotal":"200572928"},"startTime":1756896794450,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":487862,"timestamp":2066118587,"id":20,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756896794020,"traceId":"5466b6a4b3692588"},{"name":"handle-request","duration":759445,"timestamp":2065977242,"id":17,"tags":{"url":"/"},"startTime":1756896793878,"traceId":"5466b6a4b3692588"},{"name":"memory-usage","duration":12,"timestamp":2066736809,"id":22,"parentId":17,"tags":{"url":"/","memory.rss":"1051869184","memory.heapUsed":"153535992","memory.heapTotal":"201048064"},"startTime":1756896794638,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":111988,"timestamp":2067139316,"id":24,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1756896795040,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":92729,"timestamp":2067257016,"id":25,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1756896795158,"traceId":"5466b6a4b3692588"},{"name":"handle-request","duration":469016,"timestamp":2067134281,"id":23,"tags":{"url":"/favicon.ico?favicon.0b3bf435.ico"},"startTime":1756896795035,"traceId":"5466b6a4b3692588"},{"name":"memory-usage","duration":11,"timestamp":2067603425,"id":26,"parentId":23,"tags":{"url":"/favicon.ico?favicon.0b3bf435.ico","memory.rss":"1059274752","memory.heapUsed":"156997968","memory.heapTotal":"204374016"},"startTime":1756896795504,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":113021,"timestamp":2122807560,"id":28,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756896850708,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":108186,"timestamp":2122927272,"id":29,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756896850828,"traceId":"5466b6a4b3692588"},{"name":"handle-request","duration":403312,"timestamp":2122804474,"id":27,"tags":{"url":"/"},"startTime":1756896850705,"traceId":"5466b6a4b3692588"},{"name":"memory-usage","duration":23,"timestamp":2123208701,"id":30,"parentId":27,"tags":{"url":"/","memory.rss":"820207616","memory.heapUsed":"145241464","memory.heapTotal":"152858624"},"startTime":1756896851109,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":120579,"timestamp":2123644481,"id":32,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1756896851545,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":127848,"timestamp":2123771100,"id":33,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1756896851672,"traceId":"5466b6a4b3692588"},{"name":"handle-request","duration":502370,"timestamp":2123640561,"id":31,"tags":{"url":"/favicon.ico?favicon.0b3bf435.ico"},"startTime":1756896851541,"traceId":"5466b6a4b3692588"},{"name":"memory-usage","duration":21,"timestamp":2124143121,"id":34,"parentId":31,"tags":{"url":"/favicon.ico?favicon.0b3bf435.ico","memory.rss":"825344000","memory.heapUsed":"148605944","memory.heapTotal":"159617024"},"startTime":1756896852044,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":193008,"timestamp":2145083102,"id":36,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756896872984,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":100523,"timestamp":2145280621,"id":37,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756896873181,"traceId":"5466b6a4b3692588"},{"name":"handle-request","duration":474559,"timestamp":2145078920,"id":35,"tags":{"url":"/"},"startTime":1756896872979,"traceId":"5466b6a4b3692588"},{"name":"memory-usage","duration":9,"timestamp":2145553575,"id":38,"parentId":35,"tags":{"url":"/","memory.rss":"828817408","memory.heapUsed":"153017056","memory.heapTotal":"160141312"},"startTime":1756896873454,"traceId":"5466b6a4b3692588"},{"name":"client-hmr-latency","duration":232000,"timestamp":2160512864,"id":39,"parentId":3,"tags":{"updatedModules":["[project]/src/components/auth/sign-in-form.tsx"],"page":"/","isPageHidden":true},"startTime":1756896888665,"traceId":"5466b6a4b3692588"},{"name":"client-hmr-latency","duration":246000,"timestamp":2160513030,"id":40,"parentId":3,"tags":{"updatedModules":["[project]/src/components/auth/sign-in-form.tsx"],"page":"/","isPageHidden":false},"startTime":1756896888674,"traceId":"5466b6a4b3692588"},{"name":"compile-path","duration":167020,"timestamp":2220820610,"id":43,"tags":{"trigger":"/"},"startTime":1756896948722,"traceId":"5466b6a4b3692588"}]
[{"name":"client-hmr-latency","duration":1416000,"timestamp":2219472517,"id":45,"parentId":3,"tags":{"updatedModules":["[project]/src/components/auth/sign-in-form.tsx","[project]/node_modules/motion-dom/dist/es/render/utils/keys-transform.mjs","[project]/node_modules/motion-dom/dist/es/render/dom/parse-transform.mjs","[project]/node_modules/motion-dom/dist/es/animation/utils/is-css-variable.mjs","[project]/node_modules/motion-dom/dist/es/utils/mix/number.mjs","[project]/node_modules/motion-dom/dist/es/render/utils/keys-position.mjs","[project]/node_modules/motion-dom/dist/es/value/types/auto.mjs","[project]/node_modules/motion-dom/dist/es/value/types/numbers/index.mjs","[project]/node_modules/motion-dom/dist/es/value/types/numbers/units.mjs","[project]/node_modules/motion-dom/dist/es/value/types/test.mjs","[project]/node_modules/motion-dom/dist/es/value/types/dimensions.mjs","[project]/node_modules/motion-dom/dist/es/animation/utils/css-variables-conversion.mjs","[project]/node_modules/motion-dom/dist/es/animation/keyframes/utils/fill-wildcards.mjs","[project]/node_modules/motion-dom/dist/es/animation/keyframes/utils/unit-conversion.mjs","[project]/node_modules/motion-dom/dist/es/frameloop/order.mjs","[project]/node_modules/motion-dom/dist/es/stats/buffer.mjs","[project]/node_modules/motion-dom/dist/es/frameloop/render-step.mjs","[project]/node_modules/motion-dom/dist/es/frameloop/batcher.mjs","[project]/node_modules/motion-dom/dist/es/frameloop/frame.mjs","[project]/node_modules/motion-dom/dist/es/animation/keyframes/KeyframesResolver.mjs","[project]/node_modules/motion-dom/dist/es/animation/keyframes/utils/is-none.mjs","[project]/node_modules/motion-dom/dist/es/value/types/utils/sanitize.mjs","[project]/node_modules/motion-dom/dist/es/value/types/utils/float-regex.mjs","[project]/node_modules/motion-dom/dist/es/value/types/utils/is-nullish.mjs","[project]/node_modules/motion-dom/dist/es/value/types/utils/single-color-regex.mjs","[project]/node_modules/motion-dom/dist/es/value/types/color/utils.mjs","[project]/node_modules/motion-dom/dist/es/value/types/color/rgba.mjs","[project]/node_modules/motion-dom/dist/es/value/types/color/hex.mjs","[project]/node_modules/motion-dom/dist/es/value/types/color/hsla.mjs","[project]/node_modules/motion-dom/dist/es/value/types/color/index.mjs","[project]/node_modules/motion-dom/dist/es/value/types/utils/color-regex.mjs","[project]/node_modules/motion-dom/dist/es/value/types/complex/index.mjs","[project]/node_modules/motion-dom/dist/es/value/types/complex/filter.mjs","[project]/node_modules/motion-dom/dist/es/value/types/int.mjs","[project]/node_modules/motion-dom/dist/es/value/types/maps/transform.mjs","[project]/node_modules/motion-dom/dist/es/value/types/maps/number.mjs","[project]/node_modules/motion-dom/dist/es/value/types/maps/defaults.mjs","[project]/node_modules/motion-dom/dist/es/value/types/utils/animatable-none.mjs","[project]/node_modules/motion-dom/dist/es/animation/keyframes/utils/make-none-animatable.mjs","[project]/node_modules/motion-dom/dist/es/animation/keyframes/DOMKeyframesResolver.mjs","[project]/node_modules/motion-dom/dist/es/value/utils/is-motion-value.mjs","[project]/node_modules/motion-dom/dist/es/frameloop/sync-time.mjs","[project]/node_modules/motion-dom/dist/es/value/index.mjs","[project]/node_modules/motion-dom/dist/es/value/types/utils/find.mjs","[project]/node_modules/motion-dom/dist/es/frameloop/microtask.mjs","[project]/node_modules/motion-dom/dist/es/value/types/utils/get-as-type.mjs","[project]/node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs","[project]/node_modules/motion-dom/dist/es/animation/utils/make-animation-instant.mjs","[project]/node_modules/motion-dom/dist/es/stats/animation-count.mjs","[project]/node_modules/motion-dom/dist/es/value/types/color/hsla-to-rgba.mjs","[project]/node_modules/motion-dom/dist/es/utils/mix/immediate.mjs","[project]/node_modules/motion-dom/dist/es/utils/mix/color.mjs","[project]/node_modules/motion-dom/dist/es/utils/mix/visibility.mjs","[project]/node_modules/motion-dom/dist/es/utils/mix/complex.mjs","[project]/node_modules/motion-dom/dist/es/utils/mix/index.mjs","[project]/node_modules/motion-dom/dist/es/animation/drivers/frame.mjs","[project]/node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs","[project]/node_modules/motion-dom/dist/es/animation/generators/utils/calc-duration.mjs","[project]/node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs","[project]/node_modules/motion-dom/dist/es/animation/generators/utils/velocity.mjs","[project]/node_modules/motion-dom/dist/es/animation/generators/spring/defaults.mjs","[project]/node_modules/motion-dom/dist/es/animation/generators/spring/find.mjs","[project]/node_modules/motion-dom/dist/es/animation/generators/spring/index.mjs","[project]/node_modules/motion-dom/dist/es/animation/generators/inertia.mjs","[project]/node_modules/motion-dom/dist/es/utils/interpolate.mjs","[project]/node_modules/motion-dom/dist/es/animation/keyframes/offsets/fill.mjs","[project]/node_modules/motion-dom/dist/es/animation/keyframes/offsets/default.mjs","[project]/node_modules/motion-dom/dist/es/animation/keyframes/offsets/time.mjs","[project]/node_modules/motion-dom/dist/es/animation/generators/keyframes.mjs","[project]/node_modules/motion-dom/dist/es/animation/keyframes/get-final.mjs","[project]/node_modules/motion-dom/dist/es/animation/utils/replace-transition-type.mjs","[project]/node_modules/motion-dom/dist/es/animation/utils/WithPromise.mjs","[project]/node_modules/motion-dom/dist/es/animation/JSAnimation.mjs","[project]/node_modules/motion-dom/dist/es/render/dom/is-css-var.mjs","[project]/node_modules/motion-dom/dist/es/render/dom/style-set.mjs","[project]/node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs","[project]/node_modules/motion-dom/dist/es/utils/supports/flags.mjs","[project]/node_modules/motion-dom/dist/es/utils/supports/memo.mjs","[project]/node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs","[project]/node_modules/motion-dom/dist/es/animation/waapi/easing/cubic-bezier.mjs","[project]/node_modules/motion-dom/dist/es/animation/waapi/easing/supported.mjs","[project]/node_modules/motion-dom/dist/es/animation/waapi/easing/map-easing.mjs","[project]/node_modules/motion-dom/dist/es/animation/waapi/start-waapi-animation.mjs","[project]/node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs","[project]/node_modules/motion-dom/dist/es/animation/waapi/utils/apply-generator.mjs","[project]/node_modules/motion-dom/dist/es/animation/NativeAnimation.mjs","[project]/node_modules/motion-dom/dist/es/animation/waapi/utils/unsupported-easing.mjs","[project]/node_modules/motion-dom/dist/es/animation/NativeAnimationExtended.mjs","[project]/node_modules/motion-dom/dist/es/animation/utils/is-animatable.mjs","[project]/node_modules/motion-dom/dist/es/animation/utils/can-animate.mjs","[project]/node_modules/motion-dom/dist/es/animation/waapi/supports/waapi.mjs","[project]/node_modules/motion-dom/dist/es/animation/AsyncMotionValueAnimation.mjs","[project]/node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs","[project]/node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs","[project]/node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs","[project]/node_modules/motion-dom/dist/es/utils/is-svg-element.mjs","[project]/node_modules/motion-dom/dist/es/utils/is-svg-svg-element.mjs","[project]/node_modules/motion-dom/dist/es/utils/resolve-elements.mjs","[project]/node_modules/motion-dom/dist/es/gestures/utils/setup.mjs","[project]/node_modules/motion-dom/dist/es/gestures/hover.mjs","[project]/node_modules/motion-dom/dist/es/utils/is-html-element.mjs","[project]/node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs","[project]/node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs","[project]/node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs","[project]/node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs","[project]/node_modules/motion-dom/dist/es/gestures/press/index.mjs","[project]/node_modules/framer-motion/dist/es/projection/geometry/conversion.mjs","[project]/node_modules/framer-motion/dist/es/projection/utils/has-transform.mjs","[project]/node_modules/framer-motion/dist/es/projection/geometry/delta-apply.mjs","[project]/node_modules/framer-motion/dist/es/projection/utils/measure.mjs","[project]/node_modules/framer-motion/dist/es/motion/features/definitions.mjs","[project]/node_modules/framer-motion/dist/es/projection/geometry/models.mjs","[project]/node_modules/framer-motion/dist/es/utils/is-browser.mjs","[project]/node_modules/framer-motion/dist/es/utils/reduced-motion/state.mjs","[project]/node_modules/framer-motion/dist/es/utils/reduced-motion/index.mjs","[project]/node_modules/framer-motion/dist/es/render/store.mjs","[project]/node_modules/framer-motion/dist/es/animation/utils/is-animation-controls.mjs","[project]/node_modules/framer-motion/dist/es/render/utils/is-variant-label.mjs","[project]/node_modules/framer-motion/dist/es/render/utils/variant-props.mjs","[project]/node_modules/framer-motion/dist/es/render/utils/is-controlling-variants.mjs","[project]/node_modules/framer-motion/dist/es/render/utils/motion-values.mjs","[project]/node_modules/framer-motion/dist/es/render/utils/resolve-variants.mjs","[project]/node_modules/framer-motion/dist/es/render/VisualElement.mjs","[project]/node_modules/framer-motion/dist/es/render/dom/DOMVisualElement.mjs","[project]/node_modules/framer-motion/dist/es/render/html/utils/build-transform.mjs","[project]/node_modules/framer-motion/dist/es/render/html/utils/build-styles.mjs","[project]/node_modules/framer-motion/dist/es/render/html/utils/render.mjs","[project]/node_modules/framer-motion/dist/es/projection/styles/scale-correction.mjs","[project]/node_modules/framer-motion/dist/es/motion/utils/is-forced-motion-value.mjs","[project]/node_modules/framer-motion/dist/es/render/html/utils/scrape-motion-values.mjs","[project]/node_modules/framer-motion/dist/es/render/html/HTMLVisualElement.mjs","[project]/node_modules/framer-motion/dist/es/render/dom/utils/camel-to-dash.mjs","[project]/node_modules/framer-motion/dist/es/render/svg/utils/path.mjs","[project]/node_modules/framer-motion/dist/es/render/svg/utils/build-attrs.mjs","[project]/node_modules/framer-motion/dist/es/render/svg/utils/camel-case-attrs.mjs","[project]/node_modules/framer-motion/dist/es/render/svg/utils/is-svg-tag.mjs","[project]/node_modules/framer-motion/dist/es/render/svg/utils/render.mjs","[project]/node_modules/framer-motion/dist/es/render/svg/utils/scrape-motion-values.mjs","[project]/node_modules/framer-motion/dist/es/render/svg/SVGVisualElement.mjs","[project]/node_modules/framer-motion/dist/es/render/svg/lowercase-elements.mjs","[project]/node_modules/framer-motion/dist/es/render/dom/utils/is-svg-component.mjs","[project]/node_modules/framer-motion/dist/es/render/dom/create-visual-element.mjs","[project]/node_modules/framer-motion/dist/es/context/LayoutGroupContext.mjs","[project]/node_modules/framer-motion/dist/es/context/LazyContext.mjs","[project]/node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs","[project]/node_modules/framer-motion/dist/es/context/MotionContext/index.mjs","[project]/node_modules/framer-motion/dist/es/context/MotionContext/utils.mjs","[project]/node_modules/framer-motion/dist/es/context/MotionContext/create.mjs","[project]/node_modules/framer-motion/dist/es/render/html/utils/create-render-state.mjs","[project]/node_modules/framer-motion/dist/es/render/html/use-props.mjs","[project]/node_modules/framer-motion/dist/es/render/svg/utils/create-render-state.mjs","[project]/node_modules/framer-motion/dist/es/render/svg/use-props.mjs","[project]/node_modules/framer-motion/dist/es/motion/utils/valid-prop.mjs","[project]/node_modules/framer-motion/dist/es/render/dom/utils/filter-props.mjs","[project]/node_modules/framer-motion/dist/es/render/dom/use-render.mjs","[project]/node_modules/framer-motion/dist/es/context/PresenceContext.mjs","[project]/node_modules/framer-motion/dist/es/utils/use-constant.mjs","[project]/node_modules/framer-motion/dist/es/value/utils/resolve-motion-value.mjs","[project]/node_modules/framer-motion/dist/es/motion/utils/use-visual-state.mjs","[project]/node_modules/framer-motion/dist/es/render/html/use-html-visual-state.mjs","[project]/node_modules/framer-motion/dist/es/render/svg/use-svg-visual-state.mjs","[project]/node_modules/framer-motion/dist/es/motion/features/load-features.mjs","[project]/node_modules/framer-motion/dist/es/motion/utils/symbol.mjs","[project]/node_modules/framer-motion/dist/es/utils/is-ref-object.mjs","[project]/node_modules/framer-motion/dist/es/motion/utils/use-motion-ref.mjs","[project]/node_modules/framer-motion/dist/es/animation/optimized-appear/data-id.mjs","[project]/node_modules/framer-motion/dist/es/context/SwitchLayoutGroupContext.mjs","[project]/node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs","[project]/node_modules/framer-motion/dist/es/motion/utils/use-visual-element.mjs","[project]/node_modules/framer-motion/dist/es/motion/index.mjs","[project]/node_modules/framer-motion/dist/es/render/components/create-proxy.mjs","[project]/node_modules/framer-motion/dist/es/render/utils/resolve-dynamic-variants.mjs","[project]/node_modules/framer-motion/dist/es/animation/utils/is-keyframes-target.mjs","[project]/node_modules/framer-motion/dist/es/render/utils/setters.mjs","[project]/node_modules/framer-motion/dist/es/value/use-will-change/is.mjs","[project]/node_modules/framer-motion/dist/es/value/use-will-change/add-will-change.mjs","[project]/node_modules/framer-motion/dist/es/animation/optimized-appear/get-appear-id.mjs","[project]/node_modules/framer-motion/dist/es/animation/animators/waapi/utils/get-final-keyframe.mjs","[project]/node_modules/framer-motion/dist/es/animation/utils/default-transitions.mjs","[project]/node_modules/framer-motion/dist/es/animation/utils/is-transition-defined.mjs","[project]/node_modules/framer-motion/dist/es/animation/interfaces/motion-value.mjs","[project]/node_modules/framer-motion/dist/es/animation/interfaces/visual-element-target.mjs","[project]/node_modules/framer-motion/dist/es/animation/utils/calc-child-stagger.mjs","[project]/node_modules/framer-motion/dist/es/animation/interfaces/visual-element-variant.mjs","[project]/node_modules/framer-motion/dist/es/animation/interfaces/visual-element.mjs","[project]/node_modules/framer-motion/dist/es/utils/shallow-compare.mjs","[project]/node_modules/framer-motion/dist/es/render/utils/get-variant-context.mjs","[project]/node_modules/framer-motion/dist/es/render/utils/animation-state.mjs","[project]/node_modules/framer-motion/dist/es/motion/features/Feature.mjs","[project]/node_modules/framer-motion/dist/es/motion/features/animation/index.mjs","[project]/node_modules/framer-motion/dist/es/motion/features/animation/exit.mjs","[project]/node_modules/framer-motion/dist/es/motion/features/animations.mjs","[project]/node_modules/framer-motion/dist/es/events/add-dom-event.mjs","[project]/node_modules/framer-motion/dist/es/events/event-info.mjs","[project]/node_modules/framer-motion/dist/es/events/add-pointer-event.mjs","[project]/node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs","[project]/node_modules/framer-motion/dist/es/projection/utils/each-axis.mjs","[project]/node_modules/framer-motion/dist/es/utils/get-context-window.mjs","[project]/node_modules/framer-motion/dist/es/utils/distance.mjs","[project]/node_modules/framer-motion/dist/es/gestures/pan/PanSession.mjs","[project]/node_modules/framer-motion/dist/es/gestures/drag/utils/constraints.mjs","[project]/node_modules/framer-motion/dist/es/gestures/drag/VisualElementDragControls.mjs","[project]/node_modules/framer-motion/dist/es/gestures/drag/index.mjs","[project]/node_modules/framer-motion/dist/es/gestures/pan/index.mjs","[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/use-presence.mjs","[project]/node_modules/framer-motion/dist/es/projection/node/state.mjs","[project]/node_modules/framer-motion/dist/es/projection/styles/scale-border-radius.mjs","[project]/node_modules/framer-motion/dist/es/projection/styles/scale-box-shadow.mjs","[project]/node_modules/framer-motion/dist/es/motion/features/layout/MeasureLayout.mjs","[project]/node_modules/framer-motion/dist/es/animation/animate/single-value.mjs","[project]/node_modules/framer-motion/dist/es/render/utils/compare-by-depth.mjs","[project]/node_modules/framer-motion/dist/es/render/utils/flat-tree.mjs","[project]/node_modules/framer-motion/dist/es/utils/delay.mjs","[project]/node_modules/framer-motion/dist/es/projection/animation/mix-values.mjs","[project]/node_modules/framer-motion/dist/es/projection/geometry/copy.mjs","[project]/node_modules/framer-motion/dist/es/projection/geometry/delta-remove.mjs","[project]/node_modules/framer-motion/dist/es/projection/geometry/utils.mjs","[project]/node_modules/framer-motion/dist/es/projection/shared/stack.mjs","[project]/node_modules/framer-motion/dist/es/projection/styles/transform.mjs","[project]/node_modules/framer-motion/dist/es/projection/node/create-projection-node.mjs","[project]/node_modules/framer-motion/dist/es/projection/node/DocumentProjectionNode.mjs","[project]/node_modules/framer-motion/dist/es/projection/node/HTMLProjectionNode.mjs","[project]/node_modules/framer-motion/dist/es/motion/features/drag.mjs","[project]/node_modules/framer-motion/dist/es/gestures/hover.mjs","[project]/node_modules/framer-motion/dist/es/gestures/focus.mjs","[project]/node_modules/framer-motion/dist/es/gestures/press.mjs","[project]/node_modules/framer-motion/dist/es/motion/features/viewport/observers.mjs","[project]/node_modules/framer-motion/dist/es/motion/features/viewport/index.mjs","[project]/node_modules/framer-motion/dist/es/motion/features/gestures.mjs","[project]/node_modules/framer-motion/dist/es/motion/features/layout.mjs","[project]/node_modules/framer-motion/dist/es/render/components/motion/feature-bundle.mjs","[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs","[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs","[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs","[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/utils.mjs","[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs","[project]/node_modules/motion-utils/dist/es/clamp.mjs","[project]/node_modules/motion-utils/dist/es/format-error-message.mjs","[project]/node_modules/motion-utils/dist/es/errors.mjs","[project]/node_modules/motion-utils/dist/es/is-numerical-string.mjs","[project]/node_modules/motion-utils/dist/es/noop.mjs","[project]/node_modules/motion-utils/dist/es/global-config.mjs","[project]/node_modules/motion-utils/dist/es/is-zero-value-string.mjs","[project]/node_modules/motion-utils/dist/es/warn-once.mjs","[project]/node_modules/motion-utils/dist/es/array.mjs","[project]/node_modules/motion-utils/dist/es/subscription-manager.mjs","[project]/node_modules/motion-utils/dist/es/velocity-per-second.mjs","[project]/node_modules/motion-utils/dist/es/pipe.mjs","[project]/node_modules/motion-utils/dist/es/time-conversion.mjs","[project]/node_modules/motion-utils/dist/es/easing/cubic-bezier.mjs","[project]/node_modules/motion-utils/dist/es/easing/ease.mjs","[project]/node_modules/motion-utils/dist/es/easing/utils/is-easing-array.mjs","[project]/node_modules/motion-utils/dist/es/easing/modifiers/mirror.mjs","[project]/node_modules/motion-utils/dist/es/easing/modifiers/reverse.mjs","[project]/node_modules/motion-utils/dist/es/easing/back.mjs","[project]/node_modules/motion-utils/dist/es/easing/anticipate.mjs","[project]/node_modules/motion-utils/dist/es/easing/circ.mjs","[project]/node_modules/motion-utils/dist/es/easing/utils/is-bezier-definition.mjs","[project]/node_modules/motion-utils/dist/es/easing/utils/map.mjs","[project]/node_modules/motion-utils/dist/es/progress.mjs","[project]/node_modules/motion-utils/dist/es/memo.mjs","[project]/node_modules/motion-utils/dist/es/is-object.mjs","[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js"],"page":"/","isPageHidden":true},"startTime":1756896948947,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":129380,"timestamp":2220993618,"id":44,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756896948895,"traceId":"5466b6a4b3692588"},{"name":"handle-request","duration":533447,"timestamp":2220813166,"id":41,"tags":{"url":"/?_rsc=fsnpp"},"startTime":1756896948715,"traceId":"5466b6a4b3692588"},{"name":"memory-usage","duration":11,"timestamp":2221346797,"id":46,"parentId":41,"tags":{"url":"/?_rsc=fsnpp","memory.rss":"545394688","memory.heapUsed":"162224264","memory.heapTotal":"183795712"},"startTime":1756896949248,"traceId":"5466b6a4b3692588"},{"name":"client-hmr-latency","duration":184000,"timestamp":2232036680,"id":47,"parentId":3,"tags":{"updatedModules":["[project]/src/components/ui/input.tsx"],"page":"/","isPageHidden":true},"startTime":1756896960154,"traceId":"5466b6a4b3692588"},{"name":"client-hmr-latency","duration":391000,"timestamp":2255736408,"id":51,"parentId":3,"tags":{"updatedModules":["[project]/node_modules/lucide-react/dist/esm/icons/quote.js","[project]/src/components/layout/testimonial.tsx"],"page":"/","isPageHidden":false},"startTime":1756896984032,"traceId":"5466b6a4b3692588"},{"name":"compile-path","duration":227359,"timestamp":2256008747,"id":50,"tags":{"trigger":"/"},"startTime":1756896983910,"traceId":"5466b6a4b3692588"}]
[{"name":"ensure-page","duration":114892,"timestamp":2256243220,"id":52,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756896984145,"traceId":"5466b6a4b3692588"},{"name":"handle-request","duration":523165,"timestamp":2256004463,"id":48,"tags":{"url":"/?_rsc=fsnpp"},"startTime":1756896983906,"traceId":"5466b6a4b3692588"},{"name":"memory-usage","duration":13,"timestamp":2256527804,"id":53,"parentId":48,"tags":{"url":"/?_rsc=fsnpp","memory.rss":"549142528","memory.heapUsed":"150918872","memory.heapTotal":"163106816"},"startTime":1756896984429,"traceId":"5466b6a4b3692588"},{"name":"client-hmr-latency","duration":28000,"timestamp":2290793284,"id":56,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":false},"startTime":1756897018774,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":100387,"timestamp":2290833530,"id":55,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756897018734,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":94700,"timestamp":2290938407,"id":57,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756897018839,"traceId":"5466b6a4b3692588"},{"name":"handle-request","duration":380378,"timestamp":2290827672,"id":54,"tags":{"url":"/?_rsc=fsnpp"},"startTime":1756897018729,"traceId":"5466b6a4b3692588"},{"name":"memory-usage","duration":8,"timestamp":2291208173,"id":58,"parentId":54,"tags":{"url":"/?_rsc=fsnpp","memory.rss":"543920128","memory.heapUsed":"147574280","memory.heapTotal":"167333888"},"startTime":1756897019109,"traceId":"5466b6a4b3692588"},{"name":"client-hmr-latency","duration":235000,"timestamp":2325570970,"id":59,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":false},"startTime":1756897053738,"traceId":"5466b6a4b3692588"},{"name":"client-hmr-latency","duration":33000,"timestamp":2331912179,"id":62,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":false},"startTime":1756897059896,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":87954,"timestamp":2331950115,"id":61,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756897059851,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":92898,"timestamp":2332043660,"id":63,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756897059944,"traceId":"5466b6a4b3692588"},{"name":"handle-request","duration":315025,"timestamp":2331946515,"id":60,"tags":{"url":"/?_rsc=fsnpp"},"startTime":1756897059847,"traceId":"5466b6a4b3692588"},{"name":"memory-usage","duration":13,"timestamp":2332261700,"id":64,"parentId":60,"tags":{"url":"/?_rsc=fsnpp","memory.rss":"553996288","memory.heapUsed":"160128496","memory.heapTotal":"171008000"},"startTime":1756897060163,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":174527,"timestamp":2332270119,"id":66,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756897060171,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":100999,"timestamp":2332449307,"id":67,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756897060350,"traceId":"5466b6a4b3692588"},{"name":"handle-request","duration":353942,"timestamp":2332267948,"id":65,"tags":{"url":"/?_rsc=fsnpp"},"startTime":1756897060169,"traceId":"5466b6a4b3692588"},{"name":"memory-usage","duration":19,"timestamp":2332622027,"id":68,"parentId":65,"tags":{"url":"/?_rsc=fsnpp","memory.rss":"555016192","memory.heapUsed":"162663880","memory.heapTotal":"175702016"},"startTime":1756897060523,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":2034,"timestamp":2350056529,"id":69,"parentId":3,"tags":{"inputPage":"/docs"},"startTime":1756897077957,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":466,"timestamp":2350058666,"id":70,"parentId":3,"tags":{"inputPage":"/docs"},"startTime":1756897077959,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":654,"timestamp":2350060724,"id":71,"parentId":3,"tags":{"inputPage":"/docs"},"startTime":1756897077961,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":389,"timestamp":2350061445,"id":72,"parentId":3,"tags":{"inputPage":"/docs"},"startTime":1756897077962,"traceId":"5466b6a4b3692588"},{"name":"client-hmr-latency","duration":301000,"timestamp":2350072217,"id":76,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":false},"startTime":1756897078602,"traceId":"5466b6a4b3692588"},{"name":"compile-path","duration":690580,"timestamp":2350069834,"id":75,"tags":{"trigger":"/_not-found/page"},"startTime":1756897077970,"traceId":"5466b6a4b3692588"}]
[{"name":"ensure-page","duration":89077,"timestamp":2350763135,"id":77,"parentId":3,"tags":{"inputPage":"/_not-found/page"},"startTime":1756897078664,"traceId":"5466b6a4b3692588"},{"name":"handle-request","duration":903556,"timestamp":2350063775,"id":73,"tags":{"url":"/docs?_rsc=vusbg"},"startTime":1756897077964,"traceId":"5466b6a4b3692588"},{"name":"memory-usage","duration":8,"timestamp":2350967402,"id":78,"parentId":73,"tags":{"url":"/docs?_rsc=vusbg","memory.rss":"569769984","memory.heapUsed":"162070016","memory.heapTotal":"170991616"},"startTime":1756897078868,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":705,"timestamp":2350986638,"id":79,"parentId":3,"tags":{"inputPage":"/docs"},"startTime":1756897078887,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":727,"timestamp":2350987450,"id":80,"parentId":3,"tags":{"inputPage":"/docs"},"startTime":1756897078888,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":1378,"timestamp":2350991743,"id":81,"parentId":3,"tags":{"inputPage":"/docs"},"startTime":1756897078892,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":1042,"timestamp":2350993318,"id":82,"parentId":3,"tags":{"inputPage":"/docs"},"startTime":1756897078894,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":112434,"timestamp":2350996167,"id":84,"parentId":3,"tags":{"inputPage":"/_not-found/page"},"startTime":1756897078897,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":146449,"timestamp":2351109654,"id":85,"parentId":3,"tags":{"inputPage":"/_not-found/page"},"startTime":1756897079010,"traceId":"5466b6a4b3692588"},{"name":"handle-request","duration":664068,"timestamp":2350995282,"id":83,"tags":{"url":"/docs"},"startTime":1756897078896,"traceId":"5466b6a4b3692588"},{"name":"memory-usage","duration":17,"timestamp":2351659496,"id":86,"parentId":83,"tags":{"url":"/docs","memory.rss":"584904704","memory.heapUsed":"168045096","memory.heapTotal":"186675200"},"startTime":1756897079560,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":123943,"timestamp":2353865125,"id":88,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756897081766,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":101294,"timestamp":2353993278,"id":89,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756897081894,"traceId":"5466b6a4b3692588"},{"name":"handle-request","duration":733782,"timestamp":2353861788,"id":87,"tags":{"url":"/"},"startTime":1756897081762,"traceId":"5466b6a4b3692588"},{"name":"memory-usage","duration":8,"timestamp":2354595640,"id":90,"parentId":87,"tags":{"url":"/","memory.rss":"632541184","memory.heapUsed":"189302568","memory.heapTotal":"223969280"},"startTime":1756897082496,"traceId":"5466b6a4b3692588"},{"name":"client-hmr-latency","duration":208000,"timestamp":2359705655,"id":91,"parentId":3,"tags":{"updatedModules":["[project]/src/components/auth/sign-in-form.tsx"],"page":"/","isPageHidden":false},"startTime":1756897087838,"traceId":"5466b6a4b3692588"},{"name":"client-hmr-latency","duration":297000,"timestamp":2370198001,"id":92,"parentId":3,"tags":{"updatedModules":["[project]/src/components/auth/sign-in-form.tsx"],"page":"/","isPageHidden":false},"startTime":1756897098426,"traceId":"5466b6a4b3692588"},{"name":"client-hmr-latency","duration":181000,"timestamp":2383058672,"id":93,"parentId":3,"tags":{"updatedModules":["[project]/src/components/auth/sign-in-form.tsx"],"page":"/","isPageHidden":false},"startTime":1756897111177,"traceId":"5466b6a4b3692588"},{"name":"client-hmr-latency","duration":1284000,"timestamp":2494927339,"id":94,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1756897224150,"traceId":"5466b6a4b3692588"},{"name":"client-hmr-latency","duration":1015000,"timestamp":2527205391,"id":97,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1756897256132,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":682630,"timestamp":2527646949,"id":96,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756897255548,"traceId":"5466b6a4b3692588"},{"name":"compile-path","duration":97823,"timestamp":2528337017,"id":99,"tags":{"trigger":"/"},"startTime":1756897256238,"traceId":"5466b6a4b3692588"}]
[{"name":"handle-request","duration":949549,"timestamp":2527642016,"id":95,"tags":{"url":"/?_rsc=fsnpp"},"startTime":1756897255543,"traceId":"5466b6a4b3692588"},{"name":"memory-usage","duration":9,"timestamp":2528591636,"id":100,"parentId":95,"tags":{"url":"/?_rsc=fsnpp","memory.rss":"597471232","memory.heapUsed":"200523288","memory.heapTotal":"207859712"},"startTime":1756897256493,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":100620,"timestamp":2528609652,"id":102,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756897256511,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":88539,"timestamp":2528714106,"id":103,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756897256615,"traceId":"5466b6a4b3692588"},{"name":"handle-request","duration":254599,"timestamp":2528607105,"id":101,"tags":{"url":"/?_rsc=fsnpp"},"startTime":1756897256508,"traceId":"5466b6a4b3692588"},{"name":"memory-usage","duration":8,"timestamp":2528861778,"id":104,"parentId":101,"tags":{"url":"/?_rsc=fsnpp","memory.rss":"598241280","memory.heapUsed":"202011680","memory.heapTotal":"208121856"},"startTime":1756897256763,"traceId":"5466b6a4b3692588"},{"name":"client-hmr-latency","duration":822000,"timestamp":2535719923,"id":107,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1756897264449,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":415889,"timestamp":2536236033,"id":106,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756897264137,"traceId":"5466b6a4b3692588"},{"name":"compile-path","duration":103886,"timestamp":2536657485,"id":109,"tags":{"trigger":"/"},"startTime":1756897264558,"traceId":"5466b6a4b3692588"}]
[{"name":"handle-request","duration":770949,"timestamp":2536229564,"id":105,"tags":{"url":"/?_rsc=fsnpp"},"startTime":1756897264131,"traceId":"5466b6a4b3692588"},{"name":"memory-usage","duration":9,"timestamp":2537000603,"id":110,"parentId":105,"tags":{"url":"/?_rsc=fsnpp","memory.rss":"619724800","memory.heapUsed":"191293552","memory.heapTotal":"229122048"},"startTime":1756897264902,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":107128,"timestamp":2537018296,"id":112,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756897264919,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":104599,"timestamp":2537131183,"id":113,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756897265032,"traceId":"5466b6a4b3692588"},{"name":"handle-request","duration":290167,"timestamp":2537015301,"id":111,"tags":{"url":"/?_rsc=fsnpp"},"startTime":1756897264916,"traceId":"5466b6a4b3692588"},{"name":"memory-usage","duration":12,"timestamp":2537305554,"id":114,"parentId":111,"tags":{"url":"/?_rsc=fsnpp","memory.rss":"620335104","memory.heapUsed":"193870312","memory.heapTotal":"229122048"},"startTime":1756897265207,"traceId":"5466b6a4b3692588"},{"name":"client-hmr-latency","duration":84000,"timestamp":2698868742,"id":115,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1756897426869,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":274461,"timestamp":2698976452,"id":117,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756897426877,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":136923,"timestamp":2699259025,"id":118,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756897427160,"traceId":"5466b6a4b3692588"},{"name":"handle-request","duration":645802,"timestamp":2698968869,"id":116,"tags":{"url":"/?_rsc=fsnpp"},"startTime":1756897426870,"traceId":"5466b6a4b3692588"},{"name":"memory-usage","duration":29,"timestamp":2699615024,"id":119,"parentId":116,"tags":{"url":"/?_rsc=fsnpp","memory.rss":"353611776","memory.heapUsed":"198782352","memory.heapTotal":"218603520"},"startTime":1756897427516,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":172189,"timestamp":2723327947,"id":121,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756897451229,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":103128,"timestamp":2723505791,"id":122,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1756897451407,"traceId":"5466b6a4b3692588"},{"name":"handle-request","duration":994016,"timestamp":2723324034,"id":120,"tags":{"url":"/"},"startTime":1756897451225,"traceId":"5466b6a4b3692588"},{"name":"memory-usage","duration":8,"timestamp":2724318118,"id":123,"parentId":120,"tags":{"url":"/","memory.rss":"444313600","memory.heapUsed":"216483936","memory.heapTotal":"256331776"},"startTime":1756897452219,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":131062,"timestamp":2724790005,"id":125,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1756897452691,"traceId":"5466b6a4b3692588"},{"name":"ensure-page","duration":119436,"timestamp":2724929516,"id":126,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1756897452831,"traceId":"5466b6a4b3692588"},{"name":"handle-request","duration":611347,"timestamp":2724787438,"id":124,"tags":{"url":"/favicon.ico?favicon.0b3bf435.ico"},"startTime":1756897452688,"traceId":"5466b6a4b3692588"},{"name":"memory-usage","duration":8,"timestamp":2725398866,"id":127,"parentId":124,"tags":{"url":"/favicon.ico?favicon.0b3bf435.ico","memory.rss":"462487552","memory.heapUsed":"231213248","memory.heapTotal":"260444160"},"startTime":1756897453300,"traceId":"5466b6a4b3692588"},{"name":"compile-path","duration":2089236,"timestamp":2730555850,"id":130,"tags":{"trigger":"/sign-up"},"startTime":1756897458457,"traceId":"5466b6a4b3692588"}]
[{"name":"next-dev","duration":900386081,"timestamp":2028566628,"id":1,"tags":{},"startTime":1756896756467,"traceId":"5466b6a4b3692588"}]
