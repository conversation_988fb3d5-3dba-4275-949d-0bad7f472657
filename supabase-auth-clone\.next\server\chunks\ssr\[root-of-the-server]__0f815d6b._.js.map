{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Code/client-1/supabase-auth-clone/src/components/layout/header.tsx"], "sourcesContent": ["import Link from \"next/link\"\nimport { FileText } from \"lucide-react\"\n\nexport function Header() {\n  return (\n    <header className=\"border-b border-gray-800 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <nav className=\"mx-auto flex h-14 max-w-7xl items-center justify-between px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center space-x-4\">\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"h-8 w-8 rounded bg-green-600 flex items-center justify-center\">\n              <span className=\"text-white font-bold text-sm\">S</span>\n            </div>\n            <span className=\"hidden font-bold sm:inline-block text-white\">\n              Supabase\n            </span>\n          </Link>\n        </div>\n        \n        <div className=\"flex items-center space-x-4\">\n          <Link\n            href=\"/docs\"\n            className=\"flex items-center space-x-1 text-sm text-gray-400 hover:text-white transition-colors\"\n          >\n            <FileText className=\"h-4 w-4\" />\n            <span>Documentation</span>\n          </Link>\n        </div>\n      </nav>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,uKAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAA+B;;;;;;;;;;;0CAEjD,8OAAC;gCAAK,WAAU;0CAA8C;;;;;;;;;;;;;;;;;8BAMlE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,uKAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,8OAAC,0NAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlB", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Code/client-1/supabase-auth-clone/src/components/auth/sign-up-form.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const SignUpForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call SignUpForm() from the server but SignUpForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/sign-up-form.tsx <module evaluation>\",\n    \"SignUpForm\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,aAAa,IAAA,wQAAuB,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,sEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Code/client-1/supabase-auth-clone/src/components/auth/sign-up-form.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const SignUpForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call SignUpForm() from the server but SignUpForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/sign-up-form.tsx\",\n    \"SignUpForm\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,aAAa,IAAA,wQAAuB,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,kDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Code/client-1/supabase-auth-clone/src/components/layout/testimonial.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Testimonial = registerClientReference(\n    function() { throw new Error(\"Attempted to call Testimonial() from the server but Testimonial is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/testimonial.tsx <module evaluation>\",\n    \"Testimonial\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,cAAc,IAAA,wQAAuB,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,uEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Code/client-1/supabase-auth-clone/src/components/layout/testimonial.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Testimonial = registerClientReference(\n    function() { throw new Error(\"Attempted to call Testimonial() from the server but Testimonial is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/testimonial.tsx\",\n    \"Testimonial\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,cAAc,IAAA,wQAAuB,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,mDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Code/client-1/supabase-auth-clone/src/app/sign-up/page.tsx"], "sourcesContent": ["import { Header } from \"@/components/layout/header\"\nimport { SignUpForm } from \"@/components/auth/sign-up-form\"\nimport { Testimonial } from \"@/components/layout/testimonial\"\n\nexport default function SignUp() {\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Header />\n      \n      <main className=\"flex min-h-[calc(100vh-3.5rem)]\">\n        {/* Left side - Sign up form */}\n        <div className=\"flex flex-1 flex-col justify-center px-4 py-12 sm:px-6 lg:flex-none lg:px-20 xl:px-24\">\n          <div className=\"mx-auto w-full max-w-sm lg:w-96\">\n            <SignUpForm />\n          </div>\n        </div>\n\n        {/* Right side - Testimonial */}\n        <div className=\"relative hidden w-0 flex-1 lg:block\">\n          <div className=\"absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900\">\n            <div className=\"absolute inset-0 bg-gradient-to-br from-green-900/20 via-transparent to-blue-900/20\" />\n            <Testimonial />\n          </div>\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"border-t border-gray-800 bg-background/95 backdrop-blur\">\n        <div className=\"mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8\">\n          <p className=\"text-center text-xs text-gray-400\">\n            By continuing, you agree to Supabase&apos;s{\" \"}\n            <a href=\"/terms\" className=\"text-green-400 hover:text-green-300 transition-colors\">\n              Terms of Service\n            </a>{\" \"}\n            and{\" \"}\n            <a href=\"/privacy\" className=\"text-green-400 hover:text-green-300 transition-colors\">\n              Privacy Policy\n            </a>\n            , and to receive periodic emails with updates.\n          </p>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gJAAM;;;;;0BAEP,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,8JAAU;;;;;;;;;;;;;;;kCAKf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC,0JAAW;;;;;;;;;;;;;;;;;;;;;;0BAMlB,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAAoC;4BACH;0CAC5C,8OAAC;gCAAE,MAAK;gCAAS,WAAU;0CAAwD;;;;;;4BAE9E;4BAAI;4BACL;0CACJ,8OAAC;gCAAE,MAAK;gCAAW,WAAU;0CAAwD;;;;;;4BAEjF;;;;;;;;;;;;;;;;;;;;;;;AAOhB", "debugId": null}}]}