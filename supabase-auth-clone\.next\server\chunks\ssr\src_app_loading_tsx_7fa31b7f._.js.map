{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Code/client-1/supabase-auth-clone/src/app/loading.tsx"], "sourcesContent": ["export default function Loading() {\n  return (\n    <div className=\"min-h-screen bg-background flex items-center justify-center\">\n      <div className=\"flex flex-col items-center space-y-4\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-green-500\"></div>\n        <p className=\"text-gray-400 text-sm\">Loading...</p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;;;;;;AAI7C", "debugId": null}}]}