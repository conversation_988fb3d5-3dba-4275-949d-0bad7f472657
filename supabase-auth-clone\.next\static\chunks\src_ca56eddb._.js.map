{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Code/client-1/supabase-auth-clone/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yKAAO,EAAC,IAAA,gJAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Code/client-1/supabase-auth-clone/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"default\" | \"secondary\" | \"outline\" | \"ghost\"\n  size?: \"default\" | \"sm\" | \"lg\"\n  loading?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = \"default\", size = \"default\", loading, children, disabled, ...props }, ref) => {\n    const baseStyles = \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50\"\n    \n    const variants = {\n      default: \"bg-green-600 text-white shadow hover:bg-green-700\",\n      secondary: \"bg-gray-100 text-gray-900 shadow-sm hover:bg-gray-200\",\n      outline: \"border border-gray-300 bg-transparent shadow-sm hover:bg-gray-50\",\n      ghost: \"hover:bg-gray-100\"\n    }\n    \n    const sizes = {\n      default: \"h-9 px-4 py-2\",\n      sm: \"h-8 rounded-md px-3 text-xs\",\n      lg: \"h-10 rounded-md px-8\"\n    }\n\n    return (\n      <button\n        className={cn(\n          baseStyles,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,2KAAgB,MAC7B,QAA8F;QAA7F,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO;IAC1F,MAAM,aAAa;IAEnB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,SAAS;QACT,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,IAAA,4HAAE,EACX,YACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Code/client-1/supabase-auth-clone/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  error?: boolean\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, error, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-9 w-full rounded-md border border-gray-600 bg-transparent px-3 py-1 text-sm shadow-sm transition-all duration-200 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-green-500 focus-visible:border-green-500 hover:border-gray-500 disabled:cursor-not-allowed disabled:opacity-50\",\n          error && \"border-red-500 focus-visible:ring-red-500 focus-visible:border-red-500\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,2KAAgB,MAC5B,QAAuC;QAAtC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO;IACnC,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,IAAA,4HAAE,EACX,wYACA,SAAS,0EACT;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Code/client-1/supabase-auth-clone/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface LabelProps\n  extends React.LabelHTMLAttributes<HTMLLabelElement> {}\n\nconst Label = React.forwardRef<HTMLLabelElement, LabelProps>(\n  ({ className, ...props }, ref) => (\n    <label\n      ref={ref}\n      className={cn(\n        \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nLabel.displayName = \"Label\"\n\nexport { Label }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,2KAAgB,MAC5B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACtB,6LAAC;QACC,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,8FACA;QAED,GAAG,KAAK;;;;;;;;AAIf,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Code/client-1/supabase-auth-clone/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n"], "names": [], "mappings": ";;;;AAEoB;AAFpB;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,IAAA,0MAAY,EAAC,aAAa", "debugId": null}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Code/client-1/supabase-auth-clone/src/components/auth/sign-in-form.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { useForm } from \"react-hook-form\"\nimport { zodResolver } from \"@hookform/resolvers/zod\"\nimport * as z from \"zod\"\nimport { motion, AnimatePresence } from \"framer-motion\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport { Github, Building2, AlertCircle } from \"lucide-react\"\nimport { supabase } from \"@/lib/supabase\"\n\nconst signInSchema = z.object({\n  email: z.string().email(\"Please enter a valid email address\"),\n  password: z.string().min(6, \"Password must be at least 6 characters\"),\n})\n\ntype SignInFormData = z.infer<typeof signInSchema>\n\nexport function SignInForm() {\n  const [isLoading, setIsLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n  } = useForm<SignInFormData>({\n    resolver: zod<PERSON><PERSON><PERSON><PERSON>(signInSchema),\n  })\n\n  const onSubmit = async (data: SignInFormData) => {\n    setIsLoading(true)\n    setError(null)\n\n    try {\n      const { error } = await supabase.auth.signInWithPassword({\n        email: data.email,\n        password: data.password,\n      })\n\n      if (error) {\n        setError(error.message)\n      } else {\n        // Redirect to dashboard or handle successful login\n        window.location.href = \"/dashboard\"\n      }\n    } catch (err) {\n      setError(\"An unexpected error occurred\")\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleGithubSignIn = async () => {\n    setIsLoading(true)\n    try {\n      const { error } = await supabase.auth.signInWithOAuth({\n        provider: \"github\",\n        options: {\n          redirectTo: `${window.location.origin}/dashboard`,\n        },\n      })\n      if (error) {\n        setError(error.message)\n      }\n    } catch (err) {\n      setError(\"An unexpected error occurred\")\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <motion.div\n      className=\"w-full max-w-sm space-y-6\"\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      <motion.div\n        className=\"space-y-2 text-center\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ delay: 0.2, duration: 0.5 }}\n      >\n        <h1 className=\"text-2xl font-semibold tracking-tight text-white\">\n          Welcome back\n        </h1>\n        <p className=\"text-sm text-gray-400\">\n          Sign in to your account\n        </p>\n      </motion.div>\n\n      <motion.div\n        className=\"space-y-4\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ delay: 0.4, duration: 0.5 }}\n      >\n        <motion.div\n          whileHover={{ scale: 1.02 }}\n          whileTap={{ scale: 0.98 }}\n        >\n          <Button\n            variant=\"outline\"\n            className=\"w-full bg-transparent border-gray-600 text-white hover:bg-gray-800 transition-all duration-200\"\n            onClick={handleGithubSignIn}\n            disabled={isLoading}\n          >\n            <Github className=\"mr-2 h-4 w-4\" />\n            Continue with GitHub\n          </Button>\n        </motion.div>\n\n        <motion.div\n          whileHover={{ scale: 1.02 }}\n          whileTap={{ scale: 0.98 }}\n        >\n          <Button\n            variant=\"outline\"\n            className=\"w-full bg-transparent border-gray-600 text-white hover:bg-gray-800 transition-all duration-200\"\n            disabled={isLoading}\n          >\n            <Building2 className=\"mr-2 h-4 w-4\" />\n            Continue with SSO\n          </Button>\n        </motion.div>\n\n        <div className=\"relative\">\n          <div className=\"absolute inset-0 flex items-center\">\n            <span className=\"w-full border-t border-gray-600\" />\n          </div>\n          <div className=\"relative flex justify-center text-xs uppercase\">\n            <span className=\"bg-background px-2 text-gray-400\">or</span>\n          </div>\n        </div>\n\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"email\" className=\"text-sm text-gray-300\">\n              Email\n            </Label>\n            <Input\n              id=\"email\"\n              type=\"email\"\n              placeholder=\"<EMAIL>\"\n              className=\"bg-transparent border-gray-600 text-white placeholder:text-gray-500\"\n              error={!!errors.email}\n              {...register(\"email\")}\n            />\n            {errors.email && (\n              <p className=\"text-xs text-red-400\">{errors.email.message}</p>\n            )}\n          </div>\n\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center justify-between\">\n              <Label htmlFor=\"password\" className=\"text-sm text-gray-300\">\n                Password\n              </Label>\n              <a\n                href=\"/forgot-password\"\n                className=\"text-xs text-green-400 hover:text-green-300 transition-colors\"\n              >\n                Forgot Password?\n              </a>\n            </div>\n            <Input\n              id=\"password\"\n              type=\"password\"\n              placeholder=\"••••••••\"\n              className=\"bg-transparent border-gray-600 text-white placeholder:text-gray-500\"\n              error={!!errors.password}\n              {...register(\"password\")}\n            />\n            {errors.password && (\n              <p className=\"text-xs text-red-400\">{errors.password.message}</p>\n            )}\n          </div>\n\n          <AnimatePresence>\n            {error && (\n              <motion.div\n                className=\"p-3 text-sm text-red-400 bg-red-900/20 border border-red-800 rounded-md flex items-center space-x-2\"\n                initial={{ opacity: 0, height: 0 }}\n                animate={{ opacity: 1, height: \"auto\" }}\n                exit={{ opacity: 0, height: 0 }}\n                transition={{ duration: 0.3 }}\n              >\n                <AlertCircle className=\"h-4 w-4 flex-shrink-0\" />\n                <span>{error}</span>\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          <motion.div\n            whileHover={{ scale: 1.02 }}\n            whileTap={{ scale: 0.98 }}\n          >\n            <Button\n              type=\"submit\"\n              className=\"w-full bg-green-600 hover:bg-green-700 text-white transition-all duration-200\"\n              loading={isLoading}\n            >\n              Sign In\n            </Button>\n          </motion.div>\n        </form>\n\n        <motion.div\n          className=\"text-center text-sm text-gray-400\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.6, duration: 0.5 }}\n        >\n          Don&apos;t have an account?{\" \"}\n          <a\n            href=\"/sign-up\"\n            className=\"text-green-400 hover:text-green-300 transition-colors duration-200\"\n          >\n            Sign Up Now\n          </a>\n        </motion.div>\n      </motion.div>\n    </motion.div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;AAXA;;;;;;;;;;;AAaA,MAAM,eAAe,4JAAQ,CAAC;IAC5B,OAAO,4JAAQ,GAAG,KAAK,CAAC;IACxB,UAAU,4JAAQ,GAAG,GAAG,CAAC,GAAG;AAC9B;AAIO,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAC;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAgB;IAElD,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,IAAA,4KAAO,EAAiB;QAC1B,UAAU,IAAA,gLAAW,EAAC;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,qIAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBACvD,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;YACzB;YAEA,IAAI,OAAO;gBACT,SAAS,MAAM,OAAO;YACxB,OAAO;gBACL,mDAAmD;gBACnD,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB;QACzB,aAAa;QACb,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,qIAAQ,CAAC,IAAI,CAAC,eAAe,CAAC;gBACpD,UAAU;gBACV,SAAS;oBACP,YAAY,AAAC,GAAyB,OAAvB,OAAO,QAAQ,CAAC,MAAM,EAAC;gBACxC;YACF;YACA,IAAI,OAAO;gBACT,SAAS,MAAM,OAAO;YACxB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC,uMAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;;0BAE5B,6LAAC,uMAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;oBAAK,UAAU;gBAAI;;kCAExC,6LAAC;wBAAG,WAAU;kCAAmD;;;;;;kCAGjE,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAKvC,6LAAC,uMAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;oBAAK,UAAU;gBAAI;;kCAExC,6LAAC,uMAAM,CAAC,GAAG;wBACT,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;kCAExB,cAAA,6LAAC,+IAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS;4BACT,UAAU;;8CAEV,6LAAC,mNAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAKvC,6LAAC,uMAAM,CAAC,GAAG;wBACT,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;kCAExB,cAAA,6LAAC,+IAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,UAAU;;8CAEV,6LAAC,gOAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAK1C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;;;;;;;;;;;0CAElB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAmC;;;;;;;;;;;;;;;;;kCAIvD,6LAAC;wBAAK,UAAU,aAAa;wBAAW,WAAU;;0CAChD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6IAAK;wCAAC,SAAQ;wCAAQ,WAAU;kDAAwB;;;;;;kDAGzD,6LAAC,6IAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,aAAY;wCACZ,WAAU;wCACV,OAAO,CAAC,CAAC,OAAO,KAAK;wCACpB,GAAG,SAAS,QAAQ;;;;;;oCAEtB,OAAO,KAAK,kBACX,6LAAC;wCAAE,WAAU;kDAAwB,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;0CAI7D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6IAAK;gDAAC,SAAQ;gDAAW,WAAU;0DAAwB;;;;;;0DAG5D,6LAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;kDAIH,6LAAC,6IAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,aAAY;wCACZ,WAAU;wCACV,OAAO,CAAC,CAAC,OAAO,QAAQ;wCACvB,GAAG,SAAS,WAAW;;;;;;oCAEzB,OAAO,QAAQ,kBACd,6LAAC;wCAAE,WAAU;kDAAwB,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;0CAIhE,6LAAC,+MAAe;0CACb,uBACC,6LAAC,uMAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,QAAQ;oCAAE;oCACjC,SAAS;wCAAE,SAAS;wCAAG,QAAQ;oCAAO;oCACtC,MAAM;wCAAE,SAAS;wCAAG,QAAQ;oCAAE;oCAC9B,YAAY;wCAAE,UAAU;oCAAI;;sDAE5B,6LAAC,sOAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC;sDAAM;;;;;;;;;;;;;;;;;0CAKb,6LAAC,uMAAM,CAAC,GAAG;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;0CAExB,cAAA,6LAAC,+IAAM;oCACL,MAAK;oCACL,WAAU;oCACV,SAAS;8CACV;;;;;;;;;;;;;;;;;kCAML,6LAAC,uMAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAI;;4BACzC;4BAC6B;0CAC5B,6LAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX;GAhNgB;;QAQV,4KAAO;;;KARG", "debugId": null}}]}