{"version": 3, "sources": [], "sections": [{"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Code/client-1/supabase-auth-clone/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Code/client-1/supabase-auth-clone/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"default\" | \"secondary\" | \"outline\" | \"ghost\"\n  size?: \"default\" | \"sm\" | \"lg\"\n  loading?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = \"default\", size = \"default\", loading, children, disabled, ...props }, ref) => {\n    const baseStyles = \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50\"\n    \n    const variants = {\n      default: \"bg-green-600 text-white shadow hover:bg-green-700\",\n      secondary: \"bg-gray-100 text-gray-900 shadow-sm hover:bg-gray-200\",\n      outline: \"border border-gray-300 bg-transparent shadow-sm hover:bg-gray-50\",\n      ghost: \"hover:bg-gray-100\"\n    }\n    \n    const sizes = {\n      default: \"h-9 px-4 py-2\",\n      sm: \"h-8 rounded-md px-3 text-xs\",\n      lg: \"h-10 rounded-md px-8\"\n    }\n\n    return (\n      <button\n        className={cn(\n          baseStyles,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,mNAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC5F,MAAM,aAAa;IAEnB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,SAAS;QACT,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,IAAA,yHAAE,EACX,YACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Code/client-1/supabase-auth-clone/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  error?: boolean\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, error, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-9 w-full rounded-md border border-gray-600 bg-transparent px-3 py-1 text-sm shadow-sm transition-all duration-200 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-green-500 focus-visible:border-green-500 hover:border-gray-500 disabled:cursor-not-allowed disabled:opacity-50\",\n          error && \"border-red-500 focus-visible:ring-red-500 focus-visible:border-red-500\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAOA,MAAM,sBAAQ,mNAAgB,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE;IACrC,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,IAAA,yHAAE,EACX,wYACA,SAAS,0EACT;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Code/client-1/supabase-auth-clone/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface LabelProps\n  extends React.LabelHTMLAttributes<HTMLLabelElement> {}\n\nconst Label = React.forwardRef<HTMLLabelElement, LabelProps>(\n  ({ className, ...props }, ref) => (\n    <label\n      ref={ref}\n      className={cn(\n        \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nLabel.displayName = \"Label\"\n\nexport { Label }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,mNAAgB,CAC5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EACX,8FACA;QAED,GAAG,KAAK;;;;;;AAIf,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Code/client-1/supabase-auth-clone/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,IAAA,uMAAY,EAAC,aAAa", "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Code/client-1/supabase-auth-clone/src/components/auth/sign-in-form.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { useForm } from \"react-hook-form\"\nimport { zodResolver } from \"@hookform/resolvers/zod\"\nimport * as z from \"zod\"\nimport { motion, AnimatePresence } from \"framer-motion\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport { Github, Building2, AlertCircle } from \"lucide-react\"\nimport { supabase } from \"@/lib/supabase\"\n\nconst signInSchema = z.object({\n  email: z.string().email(\"Please enter a valid email address\"),\n  password: z.string().min(6, \"Password must be at least 6 characters\"),\n})\n\ntype SignInFormData = z.infer<typeof signInSchema>\n\nexport function SignInForm() {\n  const [isLoading, setIsLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n  } = useForm<SignInFormData>({\n    resolver: zod<PERSON><PERSON><PERSON><PERSON>(signInSchema),\n  })\n\n  const onSubmit = async (data: SignInFormData) => {\n    setIsLoading(true)\n    setError(null)\n\n    try {\n      const { error } = await supabase.auth.signInWithPassword({\n        email: data.email,\n        password: data.password,\n      })\n\n      if (error) {\n        setError(error.message)\n      } else {\n        // Redirect to dashboard or handle successful login\n        window.location.href = \"/dashboard\"\n      }\n    } catch (err) {\n      setError(\"An unexpected error occurred\")\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleGithubSignIn = async () => {\n    setIsLoading(true)\n    try {\n      const { error } = await supabase.auth.signInWithOAuth({\n        provider: \"github\",\n        options: {\n          redirectTo: `${window.location.origin}/dashboard`,\n        },\n      })\n      if (error) {\n        setError(error.message)\n      }\n    } catch (err) {\n      setError(\"An unexpected error occurred\")\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <motion.div\n      className=\"w-full max-w-sm space-y-6\"\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      <motion.div\n        className=\"space-y-2 text-center\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ delay: 0.2, duration: 0.5 }}\n      >\n        <h1 className=\"text-2xl font-semibold tracking-tight text-white\">\n          Welcome back\n        </h1>\n        <p className=\"text-sm text-gray-400\">\n          Sign in to your account\n        </p>\n      </motion.div>\n\n      <motion.div\n        className=\"space-y-4\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ delay: 0.4, duration: 0.5 }}\n      >\n        <motion.div\n          whileHover={{ scale: 1.02 }}\n          whileTap={{ scale: 0.98 }}\n        >\n          <Button\n            variant=\"outline\"\n            className=\"w-full bg-transparent border-gray-600 text-white hover:bg-gray-800 transition-all duration-200\"\n            onClick={handleGithubSignIn}\n            disabled={isLoading}\n          >\n            <Github className=\"mr-2 h-4 w-4\" />\n            Continue with GitHub\n          </Button>\n        </motion.div>\n\n        <motion.div\n          whileHover={{ scale: 1.02 }}\n          whileTap={{ scale: 0.98 }}\n        >\n          <Button\n            variant=\"outline\"\n            className=\"w-full bg-transparent border-gray-600 text-white hover:bg-gray-800 transition-all duration-200\"\n            disabled={isLoading}\n          >\n            <Building2 className=\"mr-2 h-4 w-4\" />\n            Continue with SSO\n          </Button>\n        </motion.div>\n\n        <div className=\"relative\">\n          <div className=\"absolute inset-0 flex items-center\">\n            <span className=\"w-full border-t border-gray-600\" />\n          </div>\n          <div className=\"relative flex justify-center text-xs uppercase\">\n            <span className=\"bg-background px-2 text-gray-400\">or</span>\n          </div>\n        </div>\n\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"email\" className=\"text-sm text-gray-300\">\n              Email\n            </Label>\n            <Input\n              id=\"email\"\n              type=\"email\"\n              placeholder=\"<EMAIL>\"\n              className=\"bg-transparent border-gray-600 text-white placeholder:text-gray-500\"\n              error={!!errors.email}\n              {...register(\"email\")}\n              aria-describedby={errors.email ? \"email-error\" : undefined}\n              autoComplete=\"email\"\n            />\n            {errors.email && (\n              <p id=\"email-error\" className=\"text-xs text-red-400\" role=\"alert\">{errors.email.message}</p>\n            )}\n          </div>\n\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center justify-between\">\n              <Label htmlFor=\"password\" className=\"text-sm text-gray-300\">\n                Password\n              </Label>\n              <a\n                href=\"/forgot-password\"\n                className=\"text-xs text-green-400 hover:text-green-300 transition-colors\"\n              >\n                Forgot Password?\n              </a>\n            </div>\n            <Input\n              id=\"password\"\n              type=\"password\"\n              placeholder=\"••••••••\"\n              className=\"bg-transparent border-gray-600 text-white placeholder:text-gray-500\"\n              error={!!errors.password}\n              {...register(\"password\")}\n              aria-describedby={errors.password ? \"password-error\" : undefined}\n              autoComplete=\"current-password\"\n            />\n            {errors.password && (\n              <p id=\"password-error\" className=\"text-xs text-red-400\" role=\"alert\">{errors.password.message}</p>\n            )}\n          </div>\n\n          <AnimatePresence>\n            {error && (\n              <motion.div\n                className=\"p-3 text-sm text-red-400 bg-red-900/20 border border-red-800 rounded-md flex items-center space-x-2\"\n                initial={{ opacity: 0, height: 0 }}\n                animate={{ opacity: 1, height: \"auto\" }}\n                exit={{ opacity: 0, height: 0 }}\n                transition={{ duration: 0.3 }}\n                role=\"alert\"\n                aria-live=\"polite\"\n              >\n                <AlertCircle className=\"h-4 w-4 flex-shrink-0\" aria-hidden=\"true\" />\n                <span>{error}</span>\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          <motion.div\n            whileHover={{ scale: 1.02 }}\n            whileTap={{ scale: 0.98 }}\n          >\n            <Button\n              type=\"submit\"\n              className=\"w-full bg-green-600 hover:bg-green-700 text-white transition-all duration-200\"\n              loading={isLoading}\n            >\n              Sign In\n            </Button>\n          </motion.div>\n        </form>\n\n        <motion.div\n          className=\"text-center text-sm text-gray-400\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.6, duration: 0.5 }}\n        >\n          Don&apos;t have an account?{\" \"}\n          <a\n            href=\"/sign-up\"\n            className=\"text-green-400 hover:text-green-300 transition-colors duration-200\"\n          >\n            Sign Up Now\n          </a>\n        </motion.div>\n      </motion.div>\n    </motion.div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAXA;;;;;;;;;;;;AAaA,MAAM,eAAe,yJAAQ,CAAC;IAC5B,OAAO,yJAAQ,GAAG,KAAK,CAAC;IACxB,UAAU,yJAAQ,GAAG,GAAG,CAAC,GAAG;AAC9B;AAIO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAgB;IAElD,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,IAAA,yKAAO,EAAiB;QAC1B,UAAU,IAAA,6KAAW,EAAC;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBACvD,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;YACzB;YAEA,IAAI,OAAO;gBACT,SAAS,MAAM,OAAO;YACxB,OAAO;gBACL,mDAAmD;gBACnD,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB;QACzB,aAAa;QACb,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAQ,CAAC,IAAI,CAAC,eAAe,CAAC;gBACpD,UAAU;gBACV,SAAS;oBACP,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC;gBACnD;YACF;YACA,IAAI,OAAO;gBACT,SAAS,MAAM,OAAO;YACxB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC,oMAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;;0BAE5B,8OAAC,oMAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;oBAAK,UAAU;gBAAI;;kCAExC,8OAAC;wBAAG,WAAU;kCAAmD;;;;;;kCAGjE,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAKvC,8OAAC,oMAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;oBAAK,UAAU;gBAAI;;kCAExC,8OAAC,oMAAM,CAAC,GAAG;wBACT,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;kCAExB,cAAA,8OAAC,4IAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS;4BACT,UAAU;;8CAEV,8OAAC,gNAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAKvC,8OAAC,oMAAM,CAAC,GAAG;wBACT,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;kCAExB,cAAA,8OAAC,4IAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,UAAU;;8CAEV,8OAAC,6NAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAK1C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;;;;;;;;;;;0CAElB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAmC;;;;;;;;;;;;;;;;;kCAIvD,8OAAC;wBAAK,UAAU,aAAa;wBAAW,WAAU;;0CAChD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0IAAK;wCAAC,SAAQ;wCAAQ,WAAU;kDAAwB;;;;;;kDAGzD,8OAAC,0IAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,aAAY;wCACZ,WAAU;wCACV,OAAO,CAAC,CAAC,OAAO,KAAK;wCACpB,GAAG,SAAS,QAAQ;wCACrB,oBAAkB,OAAO,KAAK,GAAG,gBAAgB;wCACjD,cAAa;;;;;;oCAEd,OAAO,KAAK,kBACX,8OAAC;wCAAE,IAAG;wCAAc,WAAU;wCAAuB,MAAK;kDAAS,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;0CAI3F,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0IAAK;gDAAC,SAAQ;gDAAW,WAAU;0DAAwB;;;;;;0DAG5D,8OAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;kDAIH,8OAAC,0IAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,aAAY;wCACZ,WAAU;wCACV,OAAO,CAAC,CAAC,OAAO,QAAQ;wCACvB,GAAG,SAAS,WAAW;wCACxB,oBAAkB,OAAO,QAAQ,GAAG,mBAAmB;wCACvD,cAAa;;;;;;oCAEd,OAAO,QAAQ,kBACd,8OAAC;wCAAE,IAAG;wCAAiB,WAAU;wCAAuB,MAAK;kDAAS,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;0CAIjG,8OAAC,4MAAe;0CACb,uBACC,8OAAC,oMAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,QAAQ;oCAAE;oCACjC,SAAS;wCAAE,SAAS;wCAAG,QAAQ;oCAAO;oCACtC,MAAM;wCAAE,SAAS;wCAAG,QAAQ;oCAAE;oCAC9B,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,MAAK;oCACL,aAAU;;sDAEV,8OAAC,mOAAW;4CAAC,WAAU;4CAAwB,eAAY;;;;;;sDAC3D,8OAAC;sDAAM;;;;;;;;;;;;;;;;;0CAKb,8OAAC,oMAAM,CAAC,GAAG;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;0CAExB,cAAA,8OAAC,4IAAM;oCACL,MAAK;oCACL,WAAU;oCACV,SAAS;8CACV;;;;;;;;;;;;;;;;;kCAML,8OAAC,oMAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAI;;4BACzC;4BAC6B;0CAC5B,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Code/client-1/supabase-auth-clone/src/components/layout/testimonial.tsx"], "sourcesContent": ["\"use client\"\n\nimport { motion } from \"framer-motion\"\nimport { Quote } from \"lucide-react\"\n\nexport function Testimonial() {\n  return (\n    <div className=\"hidden lg:flex lg:flex-col lg:justify-center lg:px-8\">\n      <motion.div\n        className=\"mx-auto max-w-md\"\n        initial={{ opacity: 0, x: 20 }}\n        animate={{ opacity: 1, x: 0 }}\n        transition={{ duration: 0.8, delay: 0.3 }}\n      >\n        <div className=\"space-y-6\">\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.5, delay: 0.5 }}\n          >\n            <Quote className=\"h-8 w-8 text-green-400\" />\n          </motion.div>\n\n          <motion.blockquote\n            className=\"text-xl font-medium text-white leading-relaxed\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.7 }}\n          >\n            Wait. Is it so easy to write queries for @supabase ? It&apos;s like simple SQL stuff!\n          </motion.blockquote>\n\n          <motion.div\n            className=\"flex items-center space-x-3\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.9 }}\n          >\n            <motion.div\n              className=\"h-10 w-10 rounded-full bg-gradient-to-br from-green-400 to-blue-500 flex items-center justify-center\"\n              whileHover={{ scale: 1.1 }}\n              transition={{ type: \"spring\", stiffness: 300 }}\n            >\n              <span className=\"text-white font-semibold text-sm\">TB</span>\n            </motion.div>\n            <div>\n              <div className=\"text-sm font-medium text-white\">T0ny_Boy</div>\n              <div className=\"text-xs text-gray-400\">@T0ny_Boy</div>\n            </div>\n          </motion.div>\n        </div>\n\n        <motion.div\n          className=\"mt-12 space-y-4\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 0.5, delay: 1.1 }}\n        >\n          <div className=\"flex space-x-1\">\n            {[...Array(5)].map((_, i) => (\n              <motion.div\n                key={i}\n                className=\"h-2 w-2 rounded-full bg-green-400 opacity-60\"\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                transition={{\n                  duration: 0.3,\n                  delay: 1.3 + i * 0.1,\n                  type: \"spring\",\n                  stiffness: 300\n                }}\n              />\n            ))}\n          </div>\n          <p className=\"text-xs text-gray-500\">\n            Join thousands of developers building with Supabase\n          </p>\n        </motion.div>\n      </motion.div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,oMAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,YAAY;gBAAE,UAAU;gBAAK,OAAO;YAAI;;8BAExC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oMAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,8OAAC,6MAAK;gCAAC,WAAU;;;;;;;;;;;sCAGnB,8OAAC,oMAAM,CAAC,UAAU;4BAChB,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCACzC;;;;;;sCAID,8OAAC,oMAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;;8CAExC,8OAAC,oMAAM,CAAC,GAAG;oCACT,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAI;oCACzB,YAAY;wCAAE,MAAM;wCAAU,WAAW;oCAAI;8CAE7C,cAAA,8OAAC;wCAAK,WAAU;kDAAmC;;;;;;;;;;;8CAErD,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAiC;;;;;;sDAChD,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;8BAK7C,8OAAC,oMAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;;sCAExC,8OAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,oMAAM,CAAC,GAAG;oCAET,WAAU;oCACV,SAAS;wCAAE,OAAO;oCAAE;oCACpB,SAAS;wCAAE,OAAO;oCAAE;oCACpB,YAAY;wCACV,UAAU;wCACV,OAAO,MAAM,IAAI;wCACjB,MAAM;wCACN,WAAW;oCACb;mCATK;;;;;;;;;;sCAaX,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}]}